# ADR-001: LangGraph流式输出架构决策

## 状态
已接受 (2025-09-10)

## 背景
在实现LangGraph智能体的流式输出功能时，需要在用户体验和技术实现之间做出平衡。初始实现中，服务层封装导致了流式输出的性能问题。

## 决策
采用**直接token处理**的架构，而非复杂的累积差值计算。

### 核心原则
1. **简单性优于复杂性**: 直接使用LangGraph提供的原始token
2. **性能优先**: 避免不必要的字符串操作和累积计算
3. **用户体验**: 确保真正的逐token流式输出

### 技术选择

#### 选择: 直接Token处理
```python
# 直接使用原始token
raw_token = message_chunk.content
if raw_token:
    stream_event = StreamEvent(
        type="thinking" if is_thinking else "delta",
        content=raw_token,
        node=node_name
    )
```

#### 拒绝: 累积差值计算
```python
# ❌ 拒绝的复杂逻辑
new_token = token_content[len(accumulated_content):]
```

## 考虑的替代方案

### 方案A: 二次分割机制
**描述**: 在应用层对大段内容进行智能分割  
**优点**: 能处理LLM输出不稳定的情况  
**缺点**: 增加复杂性，可能破坏语义完整性  
**决策**: 拒绝，因为问题根源不在LLM而在封装逻辑

### 方案B: 缓冲区机制
**描述**: 使用缓冲区收集token，定时批量发送  
**优点**: 可以控制发送频率  
**缺点**: 违背了流式输出的实时性原则  
**决策**: 拒绝，不符合用户体验要求

### 方案C: 直接Token处理 (选择)
**描述**: 直接使用LangGraph提供的原始token  
**优点**: 简单、高效、符合API设计  
**缺点**: 需要正确理解底层API行为  
**决策**: 接受，最符合技术原理和用户需求

## 影响

### 正面影响
- **用户体验**: 从批量输出改善为真正的逐token流式输出
- **性能**: 减少不必要的字符串操作，提升响应速度
- **代码质量**: 简化逻辑，提高可维护性
- **调试**: 更容易理解和调试流式处理逻辑

### 负面影响
- **学习成本**: 开发者需要理解LangGraph的messages模式
- **依赖性**: 更紧密地依赖LangGraph的API设计

### 风险缓解
- **文档**: 详细记录LangGraph API的使用方式
- **测试**: 添加专门的流式输出测试用例
- **监控**: 实施流式输出质量监控

## 实现细节

### 修改的文件
- `backend/app/iot/service/langgraph_agent_service.py`: 主要逻辑修改
- `backend/app/iot/service/streaming_core_service.py`: 保留但简化使用

### 关键代码变更
```python
# 修改前 (复杂的累积逻辑)
stream_events = await self.streaming_core.process_token_stream(
    token_content, node_name, accumulated
)

# 修改后 (直接token处理)
raw_token = message_chunk.content
if raw_token:
    stream_event = StreamEvent(
        type="thinking" if is_thinking_token else "delta",
        content=raw_token,
        node=node_name,
        content_type="thinking" if is_thinking_token else "response"
    )
    yield await self.emit_stream_event(stream_event)
```

## 验证标准

### 性能指标
- **事件数量**: 应该>200个事件（vs 之前的<20个）
- **平均token长度**: 应该<20字符（vs 之前的>100字符）
- **响应延迟**: 首个token应在<1秒内返回

### 用户体验指标
- **流式感受**: 用户应能看到逐字符的输出效果
- **thinking可见性**: 思考过程应实时可见
- **工具调用**: 工具执行过程应有清晰的生命周期

## 后续行动

### 短期 (1周内)
- [ ] 完善单元测试覆盖流式输出场景
- [ ] 添加性能监控指标
- [ ] 更新API文档

### 中期 (1月内)
- [ ] 在生产环境验证性能表现
- [ ] 收集用户反馈
- [ ] 优化错误处理机制

### 长期 (3月内)
- [ ] 考虑支持更多流式模式
- [ ] 评估是否需要自定义流式协议
- [ ] 探索WebSocket替代方案

## 相关文档
- [LangGraph流式输出问题解决方案](../troubleshooting/langgraph-streaming-issue.md)
- [LangGraph官方文档](https://langchain-ai.github.io/langgraph/concepts/streaming/)

---

**作者**: AI Assistant  
**审核者**: 开发团队  
**日期**: 2025-09-10  
**版本**: 1.0
